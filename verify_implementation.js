/**
 * 验证行程创建与编辑功能实现
 * 检查所有必要的文件和功能是否正确实现
 */

const fs = require('fs');
const path = require('path');

console.log('🔍 验证行程创建与编辑功能实现');
console.log('=====================================\n');

// 检查的文件和功能
const checks = [
  {
    name: '行程表单组件',
    file: 'entry/src/main/ets/components/TripForm.ets',
    keywords: ['TripFormData', 'TripType', '@Component', 'build()']
  },
  {
    name: '行程创建页面',
    file: 'entry/src/main/ets/pages/CreateTripPage.ets',
    keywords: ['CreateTripPage', 'TripForm', 'handleCreateTrip', 'router.back']
  },
  {
    name: '行程编辑页面',
    file: 'entry/src/main/ets/pages/EditTripPage.ets',
    keywords: ['EditTripPage', 'loadTripData', 'handleSaveTrip', 'confirmDeleteTrip']
  },
  {
    name: '主页面更新',
    file: 'entry/src/main/ets/pages/Index.ets',
    keywords: ['handleCreateTrip', 'CreateTripPage', 'router.pushUrl']
  },
  {
    name: '详情页面更新',
    file: 'entry/src/main/ets/pages/TripDetailPage.ets',
    keywords: ['handleEditTrip', 'EditTripPage', 'router.pushUrl']
  },
  {
    name: '数据模型扩展',
    file: 'entry/src/main/ets/models/TripModel.ets',
    keywords: ['LEISURE', 'BUSINESS', 'FAMILY', 'ADVENTURE', 'CULTURAL', 'ROMANTIC']
  },
  {
    name: '工具函数更新',
    file: 'entry/src/main/ets/utils/TripUtils.ets',
    keywords: ['TRIP_TYPE_LABELS', 'getTripTypeIcon', '休闲旅行', '商务旅行']
  },
  {
    name: '页面路由配置',
    file: 'entry/src/main/resources/base/profile/main_pages.json',
    keywords: ['CreateTripPage', 'EditTripPage']
  }
];

let allPassed = true;
let totalChecks = 0;
let passedChecks = 0;

checks.forEach(check => {
  console.log(`📋 检查: ${check.name}`);
  console.log(`📁 文件: ${check.file}`);
  
  if (!fs.existsSync(check.file)) {
    console.log(`❌ 文件不存在`);
    allPassed = false;
    totalChecks++;
    return;
  }
  
  const content = fs.readFileSync(check.file, 'utf8');
  const missingKeywords = [];
  
  check.keywords.forEach(keyword => {
    if (!content.includes(keyword)) {
      missingKeywords.push(keyword);
    }
  });
  
  totalChecks++;
  if (missingKeywords.length === 0) {
    console.log(`✅ 所有功能已实现`);
    passedChecks++;
  } else {
    console.log(`⚠️  缺少关键词: ${missingKeywords.join(', ')}`);
    allPassed = false;
  }
  
  console.log('');
});

// 检查6种行程类型
console.log('🎯 验证6种行程类型实现:');
const tripUtilsPath = 'entry/src/main/ets/utils/TripUtils.ets';
if (fs.existsSync(tripUtilsPath)) {
  const content = fs.readFileSync(tripUtilsPath, 'utf8');
  const tripTypes = [
    { key: 'LEISURE', label: '休闲旅行', icon: '🏖️' },
    { key: 'BUSINESS', label: '商务旅行', icon: '💼' },
    { key: 'FAMILY', label: '家庭旅行', icon: '👨‍👩‍👧‍👦' },
    { key: 'ADVENTURE', label: '探险旅行', icon: '🏔️' },
    { key: 'CULTURAL', label: '文化旅行', icon: '🏛️' },
    { key: 'ROMANTIC', label: '浪漫旅行', icon: '💕' }
  ];
  
  tripTypes.forEach(type => {
    const hasKey = content.includes(type.key);
    const hasLabel = content.includes(type.label);
    console.log(`${hasKey && hasLabel ? '✅' : '❌'} ${type.icon} ${type.label} (${type.key})`);
  });
} else {
  console.log('❌ TripUtils.ets 文件不存在');
}

console.log('\n📊 总体验证结果:');
console.log(`通过检查: ${passedChecks}/${totalChecks}`);

if (allPassed) {
  console.log('\n🎉 验证成功！');
  console.log('✅ 所有功能已正确实现');
  console.log('✅ 6种行程类型完整');
  console.log('✅ 页面路由配置完成');
  console.log('✅ 组件结构完整');
  console.log('\n🚀 项目已准备好在DevEco Studio中构建和测试！');
} else {
  console.log('\n⚠️  发现一些问题，请检查上述错误');
}

console.log('\n💡 下一步:');
console.log('1. 在DevEco Studio中打开项目');
console.log('2. 等待IDE自动配置SDK');
console.log('3. 构建并运行项目');
console.log('4. 测试行程创建和编辑功能');
