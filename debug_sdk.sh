#!/bin/bash

echo "=== SDK 诊断脚本 ==="
echo

echo "1. 检查 local.properties 文件:"
cat local.properties
echo

echo "2. 检查 SDK 目录是否存在:"
if [ -d "/Users/<USER>/Library/OpenHarmony/Sdk" ]; then
    echo "✅ SDK 根目录存在"
    ls -la /Users/<USER>/Library/OpenHarmony/Sdk/
else
    echo "❌ SDK 根目录不存在"
fi
echo

echo "3. 检查 SDK 15 目录:"
if [ -d "/Users/<USER>/Library/OpenHarmony/Sdk/15" ]; then
    echo "✅ SDK 15 目录存在"
    ls -la /Users/<USER>/Library/OpenHarmony/Sdk/15/
else
    echo "❌ SDK 15 目录不存在"
fi
echo

echo "4. 检查关键 SDK 组件:"
echo "- toolchains:"
ls -la /Users/<USER>/Library/OpenHarmony/Sdk/15/toolchains/ 2>/dev/null || echo "❌ toolchains 目录不存在"
echo "- ets:"
ls -la /Users/<USER>/Library/OpenHarmony/Sdk/15/ets/ 2>/dev/null || echo "❌ ets 目录不存在"
echo "- native:"
ls -la /Users/<USER>/Library/OpenHarmony/Sdk/15/native/ 2>/dev/null || echo "❌ native 目录不存在"
echo

echo "5. 检查环境变量:"
echo "DEVECO_SDK_HOME: $DEVECO_SDK_HOME"
echo

echo "6. 检查 hvigor 版本:"
/Applications/DevEco-Studio.app/Contents/tools/node/bin/node /Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/bin/hvigor.js --version
echo

echo "7. 检查项目配置:"
echo "build-profile.json5 中的 SDK 版本:"
grep -A2 -B2 "targetSdkVersion\|compatibleSdkVersion" build-profile.json5
