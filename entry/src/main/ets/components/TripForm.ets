/**
 * 行程表单组件
 * 用于创建和编辑行程的表单界面
 */

import { TripType, TripInput } from '../models/TripModel';
import { THEME_COLORS, getTripTypeIcon, calculateDays } from '../utils/TripUtils';
import { IconText, IconConstants } from './IconText';

// 行程表单数据接口
export interface TripFormData {
  title: string;
  destination: string;
  startDate: string;
  endDate: string;
  tripType: string;
}

// 行程类型选项接口
interface TripTypeOption {
  type: string;
  label: string;
  icon: string;
  description: string;
}

@Component
export struct TripForm {
  @Link formData: TripFormData;
  @State showDestinationSuggestions: boolean = false;
  @State destinationSuggestions: string[] = ['巴黎', '东京', '纽约', '伦敦', '罗马', '悉尼', '北京', '上海'];
  
  // 行程类型选项
  private tripTypes: TripTypeOption[] = [
    { 
      type: TripType.LEISURE, 
      label: '休闲旅游', 
      icon: '🏖️',
      description: '放松身心的休闲旅行'
    },
    { 
      type: TripType.BUSINESS, 
      label: '商务出行', 
      icon: '💼',
      description: '工作会议商务活动'
    },
    { 
      type: TripType.FAMILY, 
      label: '家庭旅行', 
      icon: '👨‍👩‍👧‍👦',
      description: '与家人共度美好时光'
    },
    { 
      type: TripType.ADVENTURE, 
      label: '探险旅行', 
      icon: '🏔️',
      description: '刺激冒险的探索之旅'
    },
    { 
      type: TripType.CULTURAL, 
      label: '文化之旅', 
      icon: '🏛️',
      description: '探索历史文化遗产'
    },
    { 
      type: TripType.ROMANTIC, 
      label: '浪漫之旅', 
      icon: '💕',
      description: '情侣夫妻的浪漫旅程'
    }
  ];

  // 获取计算的天数
  getCalculatedDays(): number {
    if (this.formData.startDate && this.formData.endDate) {
      return calculateDays(this.formData.startDate, this.formData.endDate);
    }
    return 0;
  }

  // 格式化日期显示
  formatDateForDisplay(dateString: string): string {
    if (!dateString) return '';
    const date = new Date(dateString);
    const year = date.getFullYear();
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const day = date.getDate().toString().padStart(2, '0');
    return `${year}-${month}-${day}`;
  }

  build() {
    Column() {
      // 行程标题
      Column() {
        Row() {
          Text('行程标题')
            .fontSize(14)
            .fontColor(THEME_COLORS.textPrimary)
            .fontWeight(500)
          
          Text('*')
            .fontSize(14)
            .fontColor('#FF4444')
            .margin({ left: 4 })
        }
        .width('100%')
        .alignItems(VerticalAlign.Center)
        .margin({ bottom: 8 })

        TextInput({ placeholder: '例如：巴黎浪漫之旅', text: this.formData.title })
          .fontSize(16)
          .fontColor(THEME_COLORS.textPrimary)
          .backgroundColor('#F5F5F5')
          .borderRadius(8)
          .padding({ left: 12, right: 12, top: 12, bottom: 12 })
          .onChange((value: string) => {
            this.formData.title = value;
          })
      }
      .width('100%')
      .alignItems(HorizontalAlign.Start)
      .margin({ bottom: 20 })

      // 目的地
      Column() {
        Row() {
          Text('目的地')
            .fontSize(14)
            .fontColor(THEME_COLORS.textPrimary)
            .fontWeight(500)
          
          Text('*')
            .fontSize(14)
            .fontColor('#FF4444')
            .margin({ left: 4 })
        }
        .width('100%')
        .alignItems(VerticalAlign.Center)
        .margin({ bottom: 8 })

        TextInput({ placeholder: '输入目的地城市', text: this.formData.destination })
          .fontSize(16)
          .fontColor(THEME_COLORS.textPrimary)
          .backgroundColor('#F5F5F5')
          .borderRadius(8)
          .padding({ left: 12, right: 12, top: 12, bottom: 12 })
          .onChange((value: string) => {
            this.formData.destination = value;
            this.showDestinationSuggestions = value.length > 0;
          })
          .onFocus(() => {
            this.showDestinationSuggestions = this.formData.destination.length > 0;
          })
          .onBlur(() => {
            // 延迟隐藏建议，以便用户可以点击建议项
            setTimeout(() => {
              this.showDestinationSuggestions = false;
            }, 200);
          })

        // 目的地建议列表
        if (this.showDestinationSuggestions) {
          Column() {
            ForEach(this.destinationSuggestions.filter(city => 
              city.includes(this.formData.destination)
            ), (city: string) => {
              Text(city)
                .fontSize(14)
                .fontColor(THEME_COLORS.textPrimary)
                .width('100%')
                .padding({ left: 12, right: 12, top: 8, bottom: 8 })
                .onClick(() => {
                  this.formData.destination = city;
                  this.showDestinationSuggestions = false;
                })
            })
          }
          .width('100%')
          .backgroundColor('#FFFFFF')
          .borderRadius(8)
          .border({ width: 1, color: THEME_COLORS.border })
          .margin({ top: 4 })
          .maxHeight(120)
        }
      }
      .width('100%')
      .alignItems(HorizontalAlign.Start)
      .margin({ bottom: 20 })

      // 行程类型
      Column() {
        Row() {
          Text('行程类型')
            .fontSize(14)
            .fontColor(THEME_COLORS.textPrimary)
            .fontWeight(500)
          
          Text('*')
            .fontSize(14)
            .fontColor('#FF4444')
            .margin({ left: 4 })
        }
        .width('100%')
        .alignItems(VerticalAlign.Center)
        .margin({ bottom: 12 })

        // 行程类型网格
        Grid() {
          ForEach(this.tripTypes, (typeInfo: TripTypeOption) => {
            GridItem() {
              Column() {
                Text(typeInfo.icon)
                  .fontSize(24)
                  .margin({ bottom: 6 })

                Text(typeInfo.label)
                  .fontSize(12)
                  .fontColor(this.formData.tripType === typeInfo.type ? 
                    THEME_COLORS.primary : THEME_COLORS.textPrimary)
                  .fontWeight(this.formData.tripType === typeInfo.type ? 600 : 400)
                  .margin({ bottom: 2 })

                Text(typeInfo.description)
                  .fontSize(10)
                  .fontColor(THEME_COLORS.textSecondary)
                  .textAlign(TextAlign.Center)
                  .maxLines(2)
                  .textOverflow({ overflow: TextOverflow.Ellipsis })
              }
              .width('100%')
              .height(80)
              .justifyContent(FlexAlign.Center)
              .backgroundColor(this.formData.tripType === typeInfo.type ? 
                '#E8F5E8' : '#F5F5F5')
              .borderRadius(8)
              .border({ 
                width: this.formData.tripType === typeInfo.type ? 2 : 1, 
                color: this.formData.tripType === typeInfo.type ? 
                  THEME_COLORS.primary : THEME_COLORS.border
              })
              .onClick(() => {
                this.formData.tripType = typeInfo.type;
              })
            }
          })
        }
        .columnsTemplate('1fr 1fr')
        .rowsTemplate('1fr 1fr 1fr')
        .columnsGap(12)
        .rowsGap(12)
        .width('100%')
        .height(264) // 3行 * (80 + 12) - 12
      }
      .width('100%')
      .alignItems(HorizontalAlign.Start)
      .margin({ bottom: 20 })

      // 出行日期
      Column() {
        Row() {
          Text('出行日期')
            .fontSize(14)
            .fontColor(THEME_COLORS.textPrimary)
            .fontWeight(500)
          
          Text('*')
            .fontSize(14)
            .fontColor('#FF4444')
            .margin({ left: 4 })
        }
        .width('100%')
        .alignItems(VerticalAlign.Center)
        .margin({ bottom: 12 })

        // 日期选择行
        Row() {
          // 开始日期
          Column() {
            Text('开始日期')
              .fontSize(12)
              .fontColor(THEME_COLORS.textSecondary)
              .margin({ bottom: 4 })

            Button(this.formData.startDate ? 
              this.formatDateForDisplay(this.formData.startDate) : '选择日期')
              .fontSize(14)
              .fontColor(this.formData.startDate ? 
                THEME_COLORS.textPrimary : THEME_COLORS.textSecondary)
              .backgroundColor('#F5F5F5')
              .borderRadius(8)
              .width('100%')
              .height(40)
              .onClick(() => {
                // TODO: 打开日期选择器
                console.log('选择开始日期');
              })
          }
          .layoutWeight(1)

          // 分隔符
          Text('至')
            .fontSize(12)
            .fontColor(THEME_COLORS.textSecondary)
            .margin({ left: 12, right: 12 })

          // 结束日期
          Column() {
            Text('结束日期')
              .fontSize(12)
              .fontColor(THEME_COLORS.textSecondary)
              .margin({ bottom: 4 })

            Button(this.formData.endDate ? 
              this.formatDateForDisplay(this.formData.endDate) : '选择日期')
              .fontSize(14)
              .fontColor(this.formData.endDate ? 
                THEME_COLORS.textPrimary : THEME_COLORS.textSecondary)
              .backgroundColor('#F5F5F5')
              .borderRadius(8)
              .width('100%')
              .height(40)
              .onClick(() => {
                // TODO: 打开日期选择器
                console.log('选择结束日期');
              })
          }
          .layoutWeight(1)
        }
        .width('100%')
        .alignItems(VerticalAlign.Top)

        // 天数显示
        if (this.formData.startDate && this.formData.endDate) {
          Text(`共 ${this.getCalculatedDays()} 天`)
            .fontSize(12)
            .fontColor(THEME_COLORS.primary)
            .margin({ top: 8 })
        }
      }
      .width('100%')
      .alignItems(HorizontalAlign.Start)
    }
    .width('100%')
    .padding({ left: 16, right: 16 })
  }
}
