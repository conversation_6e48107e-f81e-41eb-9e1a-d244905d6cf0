/**
 * 行程相关的工具函数
 * 包含状态标签、颜色、日期格式化等实用函数
 */

import { TripStatus, TripType, ActivityType } from '../models/TripModel';

// 状态标签映射
const STATUS_LABELS: Record<string, string> = {
  [TripStatus.UPCOMING]: '即将开始',
  [TripStatus.IN_PROGRESS]: '进行中',
  [TripStatus.COMPLETED]: '已完成'
};

// 状态颜色映射
const STATUS_COLORS: Record<string, string> = {
  [TripStatus.UPCOMING]: '#007AFF',
  [TripStatus.IN_PROGRESS]: '#FF9500',
  [TripStatus.COMPLETED]: '#34C759'
};

// 行程类型标签映射
const TRIP_TYPE_LABELS: Record<string, string> = {
  [TripType.LEISURE]: '休闲旅游',
  [TripType.BUSINESS]: '商务出行',
  [TripType.FAMILY]: '家庭旅行',
  [TripType.ADVENTURE]: '探险旅行',
  [TripType.CULTURAL]: '文化之旅',
  [TripType.ROMANTIC]: '浪漫之旅'
};

// 活动类型标签映射
const ACTIVITY_TYPE_LABELS: Record<string, string> = {
  [ActivityType.SIGHTSEEING]: '观光',
  [ActivityType.DINING]: '用餐',
  [ActivityType.SHOPPING]: '购物',
  [ActivityType.TRANSPORTATION]: '交通',
  [ActivityType.ACCOMMODATION]: '住宿',
  [ActivityType.ENTERTAINMENT]: '娱乐',
  [ActivityType.OTHER]: '其他'
};

// 活动状态标签映射
const ACTIVITY_STATUS_LABELS: Record<string, string> = {
  'completed': '已完成',
  'pending': '即将开始',
  'in-progress': '进行中',
  'cancelled': '已取消'
};

// 主题颜色接口
export interface ThemeColors {
  primary: string;
  primaryLight: string;
  secondary: string;
  background: string;
  cardBackground: string;
  textPrimary: string;
  textSecondary: string;
  border: string;
  success: string;
}

// 主题颜色
export const THEME_COLORS: ThemeColors = {
  primary: '#14b8a6',
  primaryLight: '#14b8a615',
  secondary: '#8e8e93',
  background: '#f8f9fa',
  cardBackground: '#ffffff',
  textPrimary: '#000000',
  textSecondary: '#8e8e93',
  border: 'rgba(60, 60, 67, 0.12)',
  success: '#10b981'
};

/**
 * 获取状态标签
 * @param status 状态值
 * @returns 状态标签
 */
export function getStatusLabel(status: string): string {
  return STATUS_LABELS[status] || '未知';
}

/**
 * 获取状态颜色
 * @param status 状态值
 * @returns 状态颜色
 */
export function getStatusColor(status: string): string {
  return STATUS_COLORS[status] || '#8E8E93';
}

/**
 * 获取行程类型标签
 * @param type 行程类型
 * @returns 类型标签
 */
export function getTripTypeLabel(type: string): string {
  return TRIP_TYPE_LABELS[type] || '其他';
}

/**
 * 获取活动类型标签
 * @param type 活动类型
 * @returns 类型标签
 */
export function getActivityTypeLabel(type: string): string {
  return ACTIVITY_TYPE_LABELS[type] || '其他';
}

/**
 * 获取活动状态标签
 * @param completed 是否完成
 * @returns 状态标签
 */
export function getActivityStatusLabel(completed: boolean): string {
  return completed ? '已完成' : '即将开始';
}

/**
 * 获取活动类型图标
 * @param type 活动类型
 * @returns 图标字符串
 */
export function getActivityTypeIcon(type: ActivityType): string {
  switch (type) {
    case ActivityType.SIGHTSEEING:
      return '🏛️';
    case ActivityType.DINING:
      return '🍽️';
    case ActivityType.SHOPPING:
      return '🛍️';
    case ActivityType.TRANSPORTATION:
      return '✈️';
    case ActivityType.ACCOMMODATION:
      return '🏨';
    case ActivityType.ENTERTAINMENT:
      return '🎭';
    default:
      return '📍';
  }
}

/**
 * 获取行程类型图标
 * @param type 行程类型
 * @returns 图标字符串
 */
export function getTripTypeIcon(type: string): string {
  switch (type) {
    case TripType.LEISURE:
      return '🏖️';
    case TripType.BUSINESS:
      return '💼';
    case TripType.FAMILY:
      return '👨‍👩‍👧‍👦';
    case TripType.ADVENTURE:
      return '🏔️';
    case TripType.CULTURAL:
      return '🏛️';
    case TripType.ROMANTIC:
      return '💕';
    default:
      return '✈️';
  }
}

/**
 * 格式化日期范围
 * @param startDate 开始日期
 * @param endDate 结束日期
 * @returns 格式化的日期范围字符串
 */
export function formatDateRange(startDate: string, endDate: string): string {
  const start = new Date(startDate);
  const end = new Date(endDate);
  const startMonth = start.getMonth() + 1;
  const startDay = start.getDate();
  const endMonth = end.getMonth() + 1;
  const endDay = end.getDate();

  if (startMonth === endMonth) {
    return `${startMonth}月${startDay}-${endDay}日`;
  } else {
    return `${startMonth}月${startDay}日 - ${endMonth}月${endDay}日`;
  }
}

/**
 * 格式化单个日期
 * @param dateString 日期字符串
 * @returns 格式化的日期
 */
export function formatDate(dateString: string): string {
  const date = new Date(dateString);
  const month = date.getMonth() + 1;
  const day = date.getDate();
  return `${month}月${day}日`;
}

/**
 * 计算两个日期之间的天数
 * @param startDate 开始日期
 * @param endDate 结束日期
 * @returns 天数
 */
export function calculateDays(startDate: string, endDate: string): number {
  const start = new Date(startDate);
  const end = new Date(endDate);
  const timeDiff = end.getTime() - start.getTime();
  return Math.ceil(timeDiff / (1000 * 3600 * 24)) + 1;
}

/**
 * 获取进度条颜色
 * @param progress 进度值
 * @param status 状态
 * @returns 进度条颜色
 */
export function getProgressColor(progress: number, status: string): string {
  if (progress === 100) {
    return STATUS_COLORS[TripStatus.COMPLETED];
  }
  return getStatusColor(status);
}

/**
 * 验证日期格式
 * @param dateString 日期字符串
 * @returns 是否为有效日期
 */
export function isValidDate(dateString: string): boolean {
  const date = new Date(dateString);
  return !isNaN(date.getTime());
}

/**
 * 获取相对时间描述
 * @param dateString 日期字符串
 * @returns 相对时间描述
 */
export function getRelativeTime(dateString: string): string {
  const date = new Date(dateString);
  const now = new Date();
  const diffTime = date.getTime() - now.getTime();
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

  if (diffDays < 0) {
    return `${Math.abs(diffDays)}天前`;
  } else if (diffDays === 0) {
    return '今天';
  } else if (diffDays === 1) {
    return '明天';
  } else if (diffDays <= 7) {
    return `${diffDays}天后`;
  } else if (diffDays <= 30) {
    const weeks = Math.ceil(diffDays / 7);
    return `${weeks}周后`;
  } else {
    const months = Math.ceil(diffDays / 30);
    return `${months}个月后`;
  }
}

/**
 * 生成唯一ID
 * @returns 唯一ID
 */
export function generateId(): number {
  return Date.now() + Math.floor(Math.random() * 1000);
}

/**
 * 深拷贝对象
 * @param obj 要拷贝的对象
 * @returns 拷贝后的对象
 */
export function deepClone<T>(obj: T): T {
  return JSON.parse(JSON.stringify(obj));
}
