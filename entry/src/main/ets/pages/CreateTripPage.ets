/**
 * 创建新行程页面
 * 用于创建新的行程
 */

import { Trip, TripType, TripStatus, TripDataManager, TripInput } from '../models/TripModel';
import { TripForm, TripFormData } from '../components/TripForm';
import { THEME_COLORS, calculateDays, generateId } from '../utils/TripUtils';
import { IconText, IconConstants } from '../components/IconText';
import router from '@ohos.router';

@Entry
@Component
struct CreateTripPage {
  @State formData: TripFormData = {
    title: '',
    destination: '',
    startDate: '',
    endDate: '',
    tripType: TripType.LEISURE
  };
  @State isSubmitting: boolean = false;
  private tripManager: TripDataManager = new TripDataManager();

  aboutToAppear() {
    console.log('CreateTripPage: aboutToAppear 被调用');
  }

  // 验证表单
  validateForm(): boolean {
    if (!this.formData.title.trim()) {
      console.error('CreateTripPage: 行程标题不能为空');
      // TODO: 显示错误提示
      return false;
    }

    if (!this.formData.destination.trim()) {
      console.error('CreateTripPage: 目的地不能为空');
      // TODO: 显示错误提示
      return false;
    }

    if (!this.formData.startDate) {
      console.error('CreateTripPage: 开始日期不能为空');
      // TODO: 显示错误提示
      return false;
    }

    if (!this.formData.endDate) {
      console.error('CreateTripPage: 结束日期不能为空');
      // TODO: 显示错误提示
      return false;
    }

    if (!this.formData.tripType) {
      console.error('CreateTripPage: 行程类型不能为空');
      // TODO: 显示错误提示
      return false;
    }

    // 验证日期逻辑
    const startDate = new Date(this.formData.startDate);
    const endDate = new Date(this.formData.endDate);
    
    if (endDate < startDate) {
      console.error('CreateTripPage: 结束日期不能早于开始日期');
      // TODO: 显示错误提示
      return false;
    }

    return true;
  }

  // 处理返回按钮
  handleBack = () => {
    router.back();
  }

  // 处理创建行程
  handleCreateTrip = () => {
    // 验证表单
    if (!this.validateForm()) {
      return;
    }

    this.isSubmitting = true;

    try {
      // 计算天数
      const daysCount = calculateDays(this.formData.startDate, this.formData.endDate);
      
      // 创建行程对象
      const newTrip: Trip = {
        id: generateId(),
        title: this.formData.title,
        destination: this.formData.destination,
        startDate: this.formData.startDate,
        endDate: this.formData.endDate,
        daysCount: daysCount,
        progress: 0,
        status: TripStatus.UPCOMING,
        tripType: this.formData.tripType
      };

      // 添加行程
      this.tripManager.addTrip(newTrip);
      
      console.log(`CreateTripPage: 成功创建行程: ${newTrip.title}`);
      
      // 显示成功提示
      // TODO: 添加Toast提示
      
      // 返回主页面
      router.back();
    } catch (error) {
      console.error('CreateTripPage: 创建行程时发生错误:', error);
      // TODO: 显示错误提示
    } finally {
      this.isSubmitting = false;
    }
  }

  build() {
    Column() {
      // 顶部导航栏
      Row() {
        Button() {
          IconText({
            iconText: IconConstants.BACK,
            fontSize: 18,
            fontColor: THEME_COLORS.textPrimary,
            iconWidth: 18,
            iconHeight: 18
          })
        }
        .type(ButtonType.Normal)
        .backgroundColor(Color.Transparent)
        .width(40)
        .height(40)
        .onClick(this.handleBack)

        Text('创建行程')
          .fontSize(18)
          .fontWeight(600)
          .fontColor(THEME_COLORS.textPrimary)
          .layoutWeight(1)
          .textAlign(TextAlign.Center)

        // 占位符，保持标题居中
        Row()
          .width(40)
          .height(40)
      }
      .width('100%')
      .height(56)
      .padding({ left: 16, right: 16 })
      .alignItems(VerticalAlign.Center)
      .backgroundColor(THEME_COLORS.background)

      // 表单内容
      Scroll() {
        Column() {
          TripForm({
            formData: $formData
          })
        }
        .width('100%')
        .padding({ top: 20, bottom: 100 })
      }
      .layoutWeight(1)
      .backgroundColor(THEME_COLORS.background)

      // 底部操作栏
      Row() {
        Button('取消')
          .fontSize(16)
          .fontColor(THEME_COLORS.textSecondary)
          .backgroundColor(Color.Transparent)
          .border({ width: 1, color: THEME_COLORS.border })
          .borderRadius(8)
          .width(80)
          .height(44)
          .onClick(this.handleBack)

        Button(this.isSubmitting ? '创建中...' : '创建行程')
          .fontSize(16)
          .fontColor('#FFFFFF')
          .backgroundColor(this.isSubmitting ? THEME_COLORS.textSecondary : THEME_COLORS.primary)
          .borderRadius(8)
          .layoutWeight(1)
          .height(44)
          .margin({ left: 12 })
          .enabled(!this.isSubmitting)
          .onClick(this.handleCreateTrip)
      }
      .width('100%')
      .padding({ left: 16, right: 16, top: 12, bottom: 12 })
      .backgroundColor(THEME_COLORS.cardBackground)
      .border({ width: { top: 1 }, color: THEME_COLORS.border })
    }
    .width('100%')
    .height('100%')
    .backgroundColor(THEME_COLORS.background)
  }
}
