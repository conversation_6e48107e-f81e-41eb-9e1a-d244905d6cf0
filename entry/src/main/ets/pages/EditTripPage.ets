/**
 * 编辑行程页面
 * 用于编辑现有行程的信息
 */

import { Trip, TripDataManager } from '../models/TripModel';
import { TripForm, TripFormData } from '../components/TripForm';
import { THEME_COLORS, calculateDays } from '../utils/TripUtils';
import { IconText, IconConstants } from '../components/IconText';
import router from '@ohos.router';

// 路由参数接口
interface RouteParams {
  tripId: number;
}

@Entry
@Component
struct EditTripPage {
  @State tripId: number = 0;
  @State trip: Trip | null = null;
  @State formData: TripFormData = {
    title: '',
    destination: '',
    startDate: '',
    endDate: '',
    tripType: ''
  };
  @State isSubmitting: boolean = false;
  @State showDeleteConfirm: boolean = false;
  private tripManager: TripDataManager = new TripDataManager();

  aboutToAppear() {
    console.log('EditTripPage: aboutToAppear 被调用');

    // 获取路由参数
    const params = router.getParams();
    console.log('EditTripPage: 路由参数:', JSON.stringify(params));

    if (params && typeof params === 'object') {
      const routeParams = params as RouteParams;
      if (routeParams.tripId !== undefined) {
        this.tripId = routeParams.tripId;
        
        console.log(`EditTripPage: 行程ID: ${this.tripId}`);
        
        // 加载行程数据
        this.loadTripData();
      }
    } else {
      console.error('EditTripPage: 未获取到有效的路由参数');
    }
  }

  // 加载行程数据
  loadTripData() {
    const trip = this.tripManager.getTripById(this.tripId);
    if (trip) {
      this.trip = trip;
      
      // 填充表单数据
      this.formData = {
        title: trip.title,
        destination: trip.destination,
        startDate: trip.startDate,
        endDate: trip.endDate,
        tripType: trip.tripType
      };
      
      console.log(`EditTripPage: 成功加载行程: ${trip.title}`);
    } else {
      console.error(`EditTripPage: 未找到ID为 ${this.tripId} 的行程`);
    }
  }

  // 验证表单
  validateForm(): boolean {
    if (!this.formData.title.trim()) {
      console.error('EditTripPage: 行程标题不能为空');
      // TODO: 显示错误提示
      return false;
    }

    if (!this.formData.destination.trim()) {
      console.error('EditTripPage: 目的地不能为空');
      // TODO: 显示错误提示
      return false;
    }

    if (!this.formData.startDate) {
      console.error('EditTripPage: 开始日期不能为空');
      // TODO: 显示错误提示
      return false;
    }

    if (!this.formData.endDate) {
      console.error('EditTripPage: 结束日期不能为空');
      // TODO: 显示错误提示
      return false;
    }

    if (!this.formData.tripType) {
      console.error('EditTripPage: 行程类型不能为空');
      // TODO: 显示错误提示
      return false;
    }

    // 验证日期逻辑
    const startDate = new Date(this.formData.startDate);
    const endDate = new Date(this.formData.endDate);
    
    if (endDate < startDate) {
      console.error('EditTripPage: 结束日期不能早于开始日期');
      // TODO: 显示错误提示
      return false;
    }

    return true;
  }

  // 处理返回按钮
  handleBack = () => {
    router.back();
  }

  // 处理保存行程
  handleSaveTrip = () => {
    // 验证表单
    if (!this.validateForm()) {
      return;
    }

    this.isSubmitting = true;

    try {
      // 计算天数
      const daysCount = calculateDays(this.formData.startDate, this.formData.endDate);
      
      // 更新行程数据
      const updatedData: Partial<Trip> = {
        title: this.formData.title,
        destination: this.formData.destination,
        startDate: this.formData.startDate,
        endDate: this.formData.endDate,
        daysCount: daysCount,
        tripType: this.formData.tripType
      };

      // 更新行程
      const success = this.tripManager.updateTrip(this.tripId, updatedData);
      
      if (success) {
        console.log(`EditTripPage: 成功更新行程`);
        
        // 显示成功提示
        // TODO: 添加Toast提示
        
        // 返回上一页
        router.back();
      } else {
        console.error('EditTripPage: 更新行程失败');
        // TODO: 显示错误提示
      }
    } catch (error) {
      console.error('EditTripPage: 更新行程时发生错误:', error);
      // TODO: 显示错误提示
    } finally {
      this.isSubmitting = false;
    }
  }

  // 处理删除行程
  handleDeleteTrip = () => {
    this.showDeleteConfirm = true;
  }

  // 确认删除行程
  confirmDeleteTrip = () => {
    try {
      const success = this.tripManager.deleteTrip(this.tripId);
      
      if (success) {
        console.log(`EditTripPage: 成功删除行程`);
        
        // 显示成功提示
        // TODO: 添加Toast提示
        
        // 返回主页面
        router.back();
      } else {
        console.error('EditTripPage: 删除行程失败');
        // TODO: 显示错误提示
      }
    } catch (error) {
      console.error('EditTripPage: 删除行程时发生错误:', error);
      // TODO: 显示错误提示
    } finally {
      this.showDeleteConfirm = false;
    }
  }

  build() {
    Column() {
      // 顶部导航栏
      Row() {
        Button() {
          IconText({
            iconText: IconConstants.BACK,
            fontSize: 18,
            fontColor: THEME_COLORS.textPrimary,
            iconWidth: 18,
            iconHeight: 18
          })
        }
        .type(ButtonType.Normal)
        .backgroundColor(Color.Transparent)
        .width(40)
        .height(40)
        .onClick(this.handleBack)

        Text('编辑行程')
          .fontSize(18)
          .fontWeight(600)
          .fontColor(THEME_COLORS.textPrimary)
          .layoutWeight(1)
          .textAlign(TextAlign.Center)

        // 删除按钮
        Button() {
          IconText({
            iconText: '🗑️',
            fontSize: 16,
            fontColor: '#FF4444',
            iconWidth: 16,
            iconHeight: 16
          })
        }
        .type(ButtonType.Normal)
        .backgroundColor(Color.Transparent)
        .width(40)
        .height(40)
        .onClick(this.handleDeleteTrip)
      }
      .width('100%')
      .height(56)
      .padding({ left: 16, right: 16 })
      .alignItems(VerticalAlign.Center)
      .backgroundColor(THEME_COLORS.background)

      // 表单内容
      if (this.trip) {
        Scroll() {
          Column() {
            TripForm({
              formData: $formData
            })
          }
          .width('100%')
          .padding({ top: 20, bottom: 100 })
        }
        .layoutWeight(1)
        .backgroundColor(THEME_COLORS.background)
      } else {
        // 加载状态
        Column() {
          Text('加载中...')
            .fontSize(16)
            .fontColor(THEME_COLORS.textSecondary)
        }
        .width('100%')
        .layoutWeight(1)
        .justifyContent(FlexAlign.Center)
        .backgroundColor(THEME_COLORS.background)
      }

      // 底部操作栏
      Row() {
        Button('取消')
          .fontSize(16)
          .fontColor(THEME_COLORS.textSecondary)
          .backgroundColor(Color.Transparent)
          .border({ width: 1, color: THEME_COLORS.border })
          .borderRadius(8)
          .width(80)
          .height(44)
          .onClick(this.handleBack)

        Button(this.isSubmitting ? '保存中...' : '保存修改')
          .fontSize(16)
          .fontColor('#FFFFFF')
          .backgroundColor(this.isSubmitting ? THEME_COLORS.textSecondary : THEME_COLORS.primary)
          .borderRadius(8)
          .layoutWeight(1)
          .height(44)
          .margin({ left: 12 })
          .enabled(!this.isSubmitting && this.trip !== null)
          .onClick(this.handleSaveTrip)
      }
      .width('100%')
      .padding({ left: 16, right: 16, top: 12, bottom: 12 })
      .backgroundColor(THEME_COLORS.cardBackground)
      .border({ width: { top: 1 }, color: THEME_COLORS.border })
    }
    .width('100%')
    .height('100%')
    .backgroundColor(THEME_COLORS.background)

    // 删除确认对话框
    if (this.showDeleteConfirm) {
      Column() {
        Column() {
          Text('确认删除')
            .fontSize(18)
            .fontWeight(600)
            .fontColor(THEME_COLORS.textPrimary)
            .margin({ bottom: 12 })

          Text('删除后无法恢复，确定要删除这个行程吗？')
            .fontSize(14)
            .fontColor(THEME_COLORS.textSecondary)
            .textAlign(TextAlign.Center)
            .margin({ bottom: 20 })

          Row() {
            Button('取消')
              .fontSize(14)
              .fontColor(THEME_COLORS.textSecondary)
              .backgroundColor(Color.Transparent)
              .border({ width: 1, color: THEME_COLORS.border })
              .borderRadius(6)
              .layoutWeight(1)
              .height(36)
              .onClick(() => {
                this.showDeleteConfirm = false;
              })

            Button('删除')
              .fontSize(14)
              .fontColor('#FFFFFF')
              .backgroundColor('#FF4444')
              .borderRadius(6)
              .layoutWeight(1)
              .height(36)
              .margin({ left: 12 })
              .onClick(this.confirmDeleteTrip)
          }
          .width('100%')
        }
        .width(280)
        .padding(20)
        .backgroundColor(THEME_COLORS.cardBackground)
        .borderRadius(12)
      }
      .width('100%')
      .height('100%')
      .backgroundColor('rgba(0, 0, 0, 0.5)')
      .justifyContent(FlexAlign.Center)
      .alignItems(HorizontalAlign.Center)
    }
  }
}
