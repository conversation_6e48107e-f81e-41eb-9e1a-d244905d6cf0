/**
 * 简单的语法检查脚本
 * 检查我们的 TypeScript/ArkTS 文件是否有基本的语法错误
 */

const fs = require('fs');
const path = require('path');

// 要检查的文件列表
const filesToCheck = [
  'entry/src/main/ets/pages/EditTripPage.ets',
  'entry/src/main/ets/pages/CreateTripPage.ets',
  'entry/src/main/ets/components/TripForm.ets',
  'entry/src/main/ets/pages/Index.ets',
  'entry/src/main/ets/pages/TripDetailPage.ets'
];

console.log('🔍 开始语法检查...\n');

let allPassed = true;

filesToCheck.forEach(filePath => {
  console.log(`检查文件: ${filePath}`);
  
  try {
    if (!fs.existsSync(filePath)) {
      console.log(`❌ 文件不存在: ${filePath}`);
      allPassed = false;
      return;
    }
    
    const content = fs.readFileSync(filePath, 'utf8');
    
    // 基本语法检查
    const issues = [];
    
    // 检查括号匹配
    const openBraces = (content.match(/\{/g) || []).length;
    const closeBraces = (content.match(/\}/g) || []).length;
    if (openBraces !== closeBraces) {
      issues.push(`括号不匹配: { ${openBraces} vs } ${closeBraces}`);
    }
    
    // 检查圆括号匹配
    const openParens = (content.match(/\(/g) || []).length;
    const closeParens = (content.match(/\)/g) || []).length;
    if (openParens !== closeParens) {
      issues.push(`圆括号不匹配: ( ${openParens} vs ) ${closeParens}`);
    }
    
    // 检查方括号匹配
    const openBrackets = (content.match(/\[/g) || []).length;
    const closeBrackets = (content.match(/\]/g) || []).length;
    if (openBrackets !== closeBrackets) {
      issues.push(`方括号不匹配: [ ${openBrackets} vs ] ${closeBrackets}`);
    }
    
    // 检查是否有基本的结构
    if (!content.includes('@Component')) {
      issues.push('缺少 @Component 装饰器');
    }
    
    if (!content.includes('build()')) {
      issues.push('缺少 build() 方法');
    }
    
    if (issues.length === 0) {
      console.log(`✅ 语法检查通过`);
    } else {
      console.log(`❌ 发现问题:`);
      issues.forEach(issue => console.log(`   - ${issue}`));
      allPassed = false;
    }
    
  } catch (error) {
    console.log(`❌ 读取文件时出错: ${error.message}`);
    allPassed = false;
  }
  
  console.log('');
});

console.log('📊 检查结果:');
if (allPassed) {
  console.log('✅ 所有文件语法检查通过！');
  console.log('🎉 代码已准备好在 DevEco Studio 中构建和测试');
} else {
  console.log('❌ 发现语法问题，请检查上述错误');
}
